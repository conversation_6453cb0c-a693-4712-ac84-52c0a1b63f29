import unittest
import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.model_service import ModelService
from services.document_parser import DocumentParser
from services.report_analyzer import ReportAnalyzer
from dotenv import load_dotenv

load_dotenv()

class TestModelService(unittest.TestCase):
    def setUp(self):
        self.model_service = ModelService()

    def test_analyze_section(self):
        return
        # 准备测试数据
        project_name = "广西电网项目"
        section_title = "测试章节"
        section_content = "这是一个测试章节内容，包含一些测试文本用于分析。"
        all_criteria = [
            {
                "id": "CR001",
                "content": "审查细则1: 项目名称应明确具体"
            },
            {
                "id": "CR002",
                "content": "审查细则2: 应包含详细的实施方案"
            }
        ]
        review_guide = "审查指南：请确保项目名称明确具体，并包含详细的实施方案。"

        # 调用被测方法
        result = self.model_service.analyze_section_batch(
            project_name,
            section_title,
            section_content,
            all_criteria,
            review_guide
        )

        # 验证返回结果是否为JSON格式
        try:
            # 验证是否包含预期的键
            self.assertIn('criteria_results', result)

            # 验证结果数量与输入的审查细则一致
            self.assertEqual(len(result['criteria_results']), len(all_criteria))
            print(f"审查结果: {result['criteria_results']}")

            # 验证每个评审结果包含必要的字段
            for criterion_result in result['criteria_results']:
                self.assertIn('criterion_id', criterion_result)
                self.assertIn('criterion_content', criterion_result)
                self.assertIn('result', criterion_result)
                self.assertIn('explanation', criterion_result)
        except Exception as e:
            self.fail(f"返回结果不是有效的JSON格式: {e}")

    def test_analyze_pdf(self):
        # 初始化服务
        model_service = ModelService()
        document_parser = DocumentParser()
        report_analyzer = ReportAnalyzer(model_service, document_parser)

        # 加载模板文件
        report_analyzer._load_templates()
        print(f"审查细则总数: {len(report_analyzer.criteria)}")

        # 调用被测方法
        section_content,result = report_analyzer.analyze_single_section("templates/test_可行性研究报告.pdf", "1 概述")

        print(f"返回结果类型: {type(result)}")
        print(f"返回结果: {result}")

        # 验证返回结果是否为JSON格式
        try:
            # 验证是否包含预期的键
            self.assertIn('criteria_results', result)
            print(f"！！！审查结果: {result['criteria_results']}")
            print(f"实际返回结果数量: {len(result['criteria_results'])}")
            print(f"期望结果数量: {len(report_analyzer.criteria)}")

            # 检查是否有错误结果
            for i, criterion_result in enumerate(result['criteria_results']):
                criterion_id = criterion_result.get('criterion_id', 'unknown')
                if criterion_id in ['api_error', 'parse_error']:
                    print(f"发现错误结果 {i}: {criterion_result}")
                    self.fail(f"API调用或解析失败: {criterion_result.get('explanation', '未知错误')}")

            # 验证结果数量与输入的审查细则一致
            self.assertEqual(len(result['criteria_results']), len(report_analyzer.criteria))

            # 验证每个评审结果包含必要的字段
            for criterion_result in result['criteria_results']:
                self.assertIn('criterion_id', criterion_result)
                self.assertIn('criterion_content', criterion_result)
                self.assertIn('result', criterion_result)
                self.assertIn('explanation', criterion_result)
        except Exception as e:
            self.fail(f"返回结果不是有效的JSON格式: {e}")


if __name__ == '__main__':
    unittest.main()